# Indian Stock Market API Integration

This document explains how the SearchBar component has been integrated with the Indian Stock Market API from [indianapi.in](https://indianapi.in/documentation/indian-stock-market).

## Features

The SearchBar component now supports two types of searches:

### 1. Stock Search
- Search for individual stocks by company name
- Supports full names, shortened names, and common names
- Returns detailed stock information including:
  - Ticker ID
  - Company name
  - Industry
  - Current prices (BSE & NSE)
  - Percentage change
  - Year high/low

### 2. Industry Search
- Search for companies within specific industries
- Returns multiple companies matching the industry criteria
- Shows company details with exchange codes and trend analysis

## Setup Instructions

### 1. Get API Key
1. Visit [indianapi.in](https://indianapi.in/)
2. Sign up for an account
3. Get your API key from the dashboard

### 2. Configure Environment Variables
1. Copy `.env.example` to `.env.local`:
   ```bash
   cp .env.example .env.local
   ```
2. Add your API key to `.env.local`:
   ```
   NEXT_PUBLIC_INDIAN_API_KEY=your_actual_api_key_here
   ```

### 3. Usage
The SearchBar component is ready to use with the following features:

#### Search Types
- **Stock Search**: Search for individual companies (e.g., "Reliance", "TCS", "HDFC")
- **Industry Search**: Search by industry sectors (e.g., "Banking", "IT", "Pharma")

#### Auto-complete
- Debounced search (500ms delay) to avoid excessive API calls
- Minimum 3 characters required for search
- Real-time results as you type

## API Endpoints Used

### Stock Search
- **Endpoint**: `/stock`
- **Method**: GET
- **Parameters**: `name` (company name)
- **Example**: `https://indianapi.in/api/stock?name=Reliance`

### Industry Search
- **Endpoint**: `/industry_search`
- **Method**: GET
- **Parameters**: `query` (industry term)
- **Example**: `https://indianapi.in/api/industry_search?query=Banking`

## Component Structure

```typescript
interface StockResult {
    tickerId: string;
    companyName: string;
    industry: string;
    currentPrice: {
        BSE?: number;
        NSE?: number;
    };
    percentChange?: number;
    yearHigh?: number;
    yearLow?: number;
}

interface IndustrySearchResult {
    id: string;
    commonName: string;
    mgIndustry: string;
    mgSector: string;
    stockType: string;
    exchangeCodeBse: string;
    exchangeCodeNsi: string;
    bseRic: string;
    nseRic: string;
    activeStockTrends: {
        shortTermTrends: string;
        longTermTrends: string;
        overallRating: string;
    };
}
```

## Error Handling

The component includes comprehensive error handling:
- Network errors
- API rate limiting
- Invalid responses
- Missing data

## Performance Optimizations

- **Debounced Search**: 500ms delay to reduce API calls
- **Result Limiting**: Industry search limited to 10 results
- **Conditional Rendering**: Only renders results when available
- **Loading States**: Visual feedback during API calls

## Styling

The component uses Tailwind CSS with dark mode support:
- Responsive design
- Hover effects
- Loading animations
- Color-coded trend indicators

## Future Enhancements

Potential improvements that could be added:
1. **Caching**: Implement result caching to reduce API calls
2. **Favorites**: Allow users to save favorite stocks
3. **Historical Data**: Add charts and historical price data
4. **Notifications**: Price alerts and notifications
5. **Portfolio**: Track user's stock portfolio

## Troubleshooting

### Common Issues

1. **No results showing**
   - Check if API key is correctly set in `.env.local`
   - Verify the API key is valid and has sufficient credits
   - Check browser console for error messages

2. **Search not working**
   - Ensure minimum 3 characters are entered
   - Check network connectivity
   - Verify API endpoint is accessible

3. **Styling issues**
   - Ensure Tailwind CSS is properly configured
   - Check for conflicting CSS classes

### API Limits
- Check your API usage limits on the indianapi.in dashboard
- Consider implementing caching for frequently searched stocks
- Monitor API response times and implement fallbacks if needed

## Support

For API-related issues, contact [indianapi.in support](https://indianapi.in/contact).
For component issues, check the browser console and network tab for debugging information.
