'use client';

import { useState } from 'react';

export default function StockDetailsPage() {
  const [stockData, setStockData] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchStockDetails = async (stockName: string) => {
    setLoading(true);
    setError(null);
    
    try {
      const apiKey = process.env.NEXT_PUBLIC_INDIAN_API_KEY;
      const response = await fetch(`https://stock.indianapi.in/stock?name=${encodeURIComponent(stockName)}`, {
        headers: {
          'x-api-key': apiKey || '',
          'Accept': 'application/json',
        }
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      setStockData(data);
      console.log('Stock Data:', data);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMessage);
      console.error('Error:', err);
    } finally {
      setLoading(false);
    }
  };

  const testStocks = ['TCS', 'Reliance', 'HDFC Bank', 'Infosys', 'ICICI Bank'];

  return (
    <div className="p-6 max-w-6xl mx-auto">
      <h1 className="text-3xl font-bold mb-6 text-gray-900 dark:text-white">Stock Details Viewer</h1>
      
      {/* Test Buttons */}
      <div className="mb-6">
        <h2 className="text-lg font-semibold mb-3 text-gray-800 dark:text-gray-200">Test with Popular Stocks:</h2>
        <div className="flex flex-wrap gap-2">
          {testStocks.map((stock) => (
            <button
              key={stock}
              onClick={() => fetchStockDetails(stock)}
              disabled={loading}
              className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {stock}
            </button>
          ))}
        </div>
      </div>

      {/* Custom Input */}
      <div className="mb-6">
        <h2 className="text-lg font-semibold mb-3 text-gray-800 dark:text-gray-200">Or Enter Custom Stock Name:</h2>
        <div className="flex gap-2">
          <input
            type="text"
            placeholder="Enter stock name (e.g., Tata Steel)"
            className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
            onKeyPress={(e) => {
              if (e.key === 'Enter') {
                const target = e.target as HTMLInputElement;
                if (target.value.trim()) {
                  fetchStockDetails(target.value.trim());
                }
              }
            }}
          />
          <button
            onClick={() => {
              const input = document.querySelector('input[type="text"]') as HTMLInputElement;
              if (input?.value.trim()) {
                fetchStockDetails(input.value.trim());
              }
            }}
            disabled={loading}
            className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50"
          >
            {loading ? 'Loading...' : 'Fetch'}
          </button>
        </div>
      </div>

      {/* Loading State */}
      {loading && (
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
          <span className="ml-2 text-gray-600 dark:text-gray-400">Fetching stock details...</span>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          <strong>Error:</strong> {error}
        </div>
      )}

      {/* Stock Data Display */}
      {stockData && (
        <div className="space-y-6">
          {/* Basic Info Card */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h2 className="text-xl font-semibold mb-4 text-gray-900 dark:text-white">
              {stockData.companyName || 'Company Name Not Available'}
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <span className="text-sm text-gray-600 dark:text-gray-400">Ticker ID:</span>
                <p className="font-medium text-gray-900 dark:text-white">{stockData.tickerId || 'N/A'}</p>
              </div>
              <div>
                <span className="text-sm text-gray-600 dark:text-gray-400">Industry:</span>
                <p className="font-medium text-gray-900 dark:text-white">{stockData.industry || 'N/A'}</p>
              </div>
              <div>
                <span className="text-sm text-gray-600 dark:text-gray-400">Change:</span>
                <p className={`font-medium ${
                  stockData.percentChange >= 0 
                    ? 'text-green-600 dark:text-green-400' 
                    : 'text-red-600 dark:text-red-400'
                }`}>
                  {stockData.percentChange !== undefined 
                    ? `${stockData.percentChange >= 0 ? '+' : ''}${stockData.percentChange.toFixed(2)}%`
                    : 'N/A'
                  }
                </p>
              </div>
            </div>
          </div>

          {/* Price Information */}
          {stockData.currentPrice && (
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
              <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">Current Prices</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {stockData.currentPrice.NSE && (
                  <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
                    <h4 className="text-sm font-medium text-blue-700 dark:text-blue-300">NSE</h4>
                    <p className="text-2xl font-bold text-blue-900 dark:text-blue-100">
                      ₹{stockData.currentPrice.NSE.toFixed(2)}
                    </p>
                  </div>
                )}
                {stockData.currentPrice.BSE && (
                  <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
                    <h4 className="text-sm font-medium text-green-700 dark:text-green-300">BSE</h4>
                    <p className="text-2xl font-bold text-green-900 dark:text-green-100">
                      ₹{stockData.currentPrice.BSE.toFixed(2)}
                    </p>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* 52 Week High/Low */}
          {(stockData.yearHigh || stockData.yearLow) && (
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
              <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">52 Week Range</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {stockData.yearHigh && (
                  <div className="text-center">
                    <span className="text-sm text-gray-600 dark:text-gray-400">52W High</span>
                    <p className="text-xl font-bold text-gray-900 dark:text-white">₹{stockData.yearHigh.toFixed(2)}</p>
                  </div>
                )}
                {stockData.yearLow && (
                  <div className="text-center">
                    <span className="text-sm text-gray-600 dark:text-gray-400">52W Low</span>
                    <p className="text-xl font-bold text-gray-900 dark:text-white">₹{stockData.yearLow.toFixed(2)}</p>
                  </div>
                )}
              </div>
            </div>
          )}

          {/* All Data in Organized Format */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">All Stock Data</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              {Object.entries(stockData).map(([key, value]) => (
                <div key={key} className="border-b border-gray-200 dark:border-gray-700 pb-2">
                  <span className="font-medium text-gray-700 dark:text-gray-300 capitalize block">
                    {key.replace(/([A-Z])/g, ' $1').trim()}:
                  </span>
                  <span className="text-gray-600 dark:text-gray-400 break-all">
                    {typeof value === 'object' && value !== null 
                      ? JSON.stringify(value, null, 2)
                      : String(value)
                    }
                  </span>
                </div>
              ))}
            </div>
          </div>

          {/* Raw JSON */}
          <details className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <summary className="text-lg font-semibold text-gray-900 dark:text-white cursor-pointer">
              Raw JSON Response
            </summary>
            <pre className="mt-4 text-xs text-gray-600 dark:text-gray-400 overflow-auto max-h-96 bg-gray-50 dark:bg-gray-900 p-4 rounded border">
              {JSON.stringify(stockData, null, 2)}
            </pre>
          </details>
        </div>
      )}
    </div>
  );
}
