"use client";
import React, { useState } from 'react';
import { 
    searchStock, 
    searchIndustry, 
    getTrendingStocks,
    get52WeekHighLow,
    getNSEMostActive,
    getBSEMostActive,
    type StockData,
    type IndustrySearchResult,
    type TrendingStocksResponse
} from '@/utils/indianStockApi';

const ApiTest = () => {
    const [results, setResults] = useState<any>(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [activeTest, setActiveTest] = useState<string>('');

    const runTest = async (testName: string, testFunction: () => Promise<any>) => {
        setLoading(true);
        setError(null);
        setActiveTest(testName);
        setResults(null);

        try {
            const result = await testFunction();
            setResults(result);
        } catch (err) {
            setError(err instanceof Error ? err.message : 'Test failed');
        } finally {
            setLoading(false);
        }
    };

    const tests = [
        {
            name: 'Stock Search - Reliance',
            description: 'Search for Reliance stock data',
            action: () => runTest('Stock Search', () => searchStock('Reliance'))
        },
        {
            name: 'Stock Search - TCS',
            description: 'Search for TCS stock data',
            action: () => runTest('Stock Search', () => searchStock('TCS'))
        },
        {
            name: 'Industry Search - Banking',
            description: 'Search for companies in Banking industry',
            action: () => runTest('Industry Search', () => searchIndustry('Banking'))
        },
        {
            name: 'Industry Search - IT',
            description: 'Search for companies in IT industry',
            action: () => runTest('Industry Search', () => searchIndustry('IT'))
        },
        {
            name: 'Trending Stocks',
            description: 'Get trending stocks (top gainers and losers)',
            action: () => runTest('Trending Stocks', () => getTrendingStocks())
        },
        {
            name: '52 Week High/Low',
            description: 'Get 52 week high and low data',
            action: () => runTest('52 Week High/Low', () => get52WeekHighLow())
        },
        {
            name: 'NSE Most Active',
            description: 'Get most active stocks on NSE',
            action: () => runTest('NSE Most Active', () => getNSEMostActive())
        },
        {
            name: 'BSE Most Active',
            description: 'Get most active stocks on BSE',
            action: () => runTest('BSE Most Active', () => getBSEMostActive())
        }
    ];

    return (
        <div className="max-w-6xl mx-auto p-6">
            <div className="mb-8">
                <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
                    Indian Stock Market API Test Suite
                </h1>
                <p className="text-gray-600 dark:text-gray-400">
                    Test various endpoints of the Indian Stock Market API to verify integration.
                </p>
            </div>

            {/* Test Buttons */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-8">
                {tests.map((test, index) => (
                    <button
                        key={index}
                        onClick={test.action}
                        disabled={loading}
                        className="p-4 text-left bg-white dark:bg-gray-800 rounded-lg shadow-md hover:shadow-lg transition-shadow disabled:opacity-50 disabled:cursor-not-allowed border border-gray-200 dark:border-gray-700"
                    >
                        <h3 className="font-semibold text-gray-900 dark:text-white mb-2">
                            {test.name}
                        </h3>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                            {test.description}
                        </p>
                    </button>
                ))}
            </div>

            {/* Loading State */}
            {loading && (
                <div className="flex items-center justify-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                    <span className="ml-3 text-gray-600 dark:text-gray-400">
                        Running {activeTest} test...
                    </span>
                </div>
            )}

            {/* Error State */}
            {error && (
                <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6">
                    <h3 className="font-semibold text-red-800 dark:text-red-200 mb-2">Test Failed</h3>
                    <p className="text-red-600 dark:text-red-400">{error}</p>
                </div>
            )}

            {/* Results */}
            {results && (
                <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
                    <div className="flex items-center justify-between mb-4">
                        <h3 className="text-xl font-semibold text-gray-900 dark:text-white">
                            {activeTest} Results
                        </h3>
                        <span className={`px-3 py-1 rounded-full text-sm font-medium ${
                            results.error 
                                ? 'bg-red-100 dark:bg-red-900/20 text-red-800 dark:text-red-200'
                                : 'bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-200'
                        }`}>
                            {results.error ? 'Error' : 'Success'}
                        </span>
                    </div>

                    {results.error ? (
                        <div className="text-red-600 dark:text-red-400">
                            <p className="font-medium mb-2">Error Message:</p>
                            <p>{results.error}</p>
                        </div>
                    ) : (
                        <div className="space-y-4">
                            {/* Data Preview */}
                            <div>
                                <h4 className="font-medium text-gray-900 dark:text-white mb-2">
                                    Data Preview:
                                </h4>
                                {results.data && typeof results.data === 'object' && (
                                    <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                                        {/* Stock Data Preview */}
                                        {results.data.tickerId && (
                                            <div className="space-y-2">
                                                <p><strong>Company:</strong> {results.data.companyName}</p>
                                                <p><strong>Ticker:</strong> {results.data.tickerId}</p>
                                                <p><strong>Industry:</strong> {results.data.industry}</p>
                                                {results.data.currentPrice && (
                                                    <div>
                                                        <strong>Current Price:</strong>
                                                        {results.data.currentPrice.NSE && ` NSE: ₹${results.data.currentPrice.NSE}`}
                                                        {results.data.currentPrice.BSE && ` BSE: ₹${results.data.currentPrice.BSE}`}
                                                    </div>
                                                )}
                                                {results.data.percentChange !== undefined && (
                                                    <p><strong>Change:</strong> {results.data.percentChange}%</p>
                                                )}
                                            </div>
                                        )}

                                        {/* Industry Search Results Preview */}
                                        {Array.isArray(results.data) && results.data.length > 0 && results.data[0].commonName && (
                                            <div>
                                                <p className="font-medium mb-2">Found {results.data.length} companies:</p>
                                                <div className="space-y-1">
                                                    {results.data.slice(0, 5).map((company: IndustrySearchResult, index: number) => (
                                                        <p key={index} className="text-sm">
                                                            {company.commonName} - {company.mgIndustry}
                                                        </p>
                                                    ))}
                                                    {results.data.length > 5 && (
                                                        <p className="text-sm text-gray-500">
                                                            ...and {results.data.length - 5} more
                                                        </p>
                                                    )}
                                                </div>
                                            </div>
                                        )}

                                        {/* Trending Stocks Preview */}
                                        {results.data.trending_stocks && (
                                            <div className="space-y-2">
                                                <p><strong>Top Gainers:</strong> {results.data.trending_stocks.top_gainers?.length || 0}</p>
                                                <p><strong>Top Losers:</strong> {results.data.trending_stocks.top_losers?.length || 0}</p>
                                            </div>
                                        )}
                                    </div>
                                )}
                            </div>

                            {/* Raw JSON */}
                            <details className="border border-gray-200 dark:border-gray-600 rounded-lg">
                                <summary className="p-3 cursor-pointer font-medium text-gray-900 dark:text-white hover:bg-gray-50 dark:hover:bg-gray-700">
                                    View Raw JSON Response
                                </summary>
                                <div className="p-3 border-t border-gray-200 dark:border-gray-600">
                                    <pre className="text-xs text-gray-600 dark:text-gray-400 overflow-auto max-h-96">
                                        {JSON.stringify(results, null, 2)}
                                    </pre>
                                </div>
                            </details>
                        </div>
                    )}
                </div>
            )}

            {/* Setup Instructions */}
            <div className="mt-8 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                <h4 className="font-semibold text-blue-800 dark:text-blue-200 mb-2">Setup Instructions</h4>
                <div className="text-blue-700 dark:text-blue-300 text-sm space-y-1">
                    <p>1. Get your API key from <a href="https://indianapi.in/" target="_blank" rel="noopener noreferrer" className="underline">indianapi.in</a></p>
                    <p>2. Add it to your .env.local file:</p>
                    <code className="block bg-blue-100 dark:bg-blue-800 px-2 py-1 rounded mt-1">
                        NEXT_PUBLIC_INDIAN_API_KEY=your_api_key_here
                    </code>
                    <p>3. Restart your development server</p>
                </div>
            </div>
        </div>
    );
};

export default ApiTest;
