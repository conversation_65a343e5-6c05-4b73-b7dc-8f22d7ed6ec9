"use client";
import React, { useState } from 'react';

interface StockData {
    tickerId: string;
    companyName: string;
    industry: string;
    currentPrice: {
        BSE?: number;
        NSE?: number;
    };
    percentChange?: number;
    yearHigh?: number;
    yearLow?: number;
}

const StockSearchExample = () => {
    const [stockData, setStockData] = useState<StockData | null>(null);
    const [loading, setLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);

    const searchStock = async (stockName: string) => {
        setLoading(true);
        setError(null);

        try {
            const response = await fetch(`https://indianapi.in/api/stock?name=${encodeURIComponent(stockName)}`, {
                headers: {
                    'Accept': 'application/json',
                    'x-api-key': process.env.NEXT_PUBLIC_INDIAN_API_KEY || '',
                }
            });

            if (!response.ok) {
                throw new Error(`API Error: ${response.status}`);
            }

            const data = await response.json();
            setStockData(data);
        } catch (err) {
            setError(err instanceof Error ? err.message : 'Failed to fetch stock data');
            setStockData(null);
        } finally {
            setLoading(false);
        }
    };

    const exampleStocks = ['Reliance', 'TCS', 'HDFC Bank', 'Infosys', 'ICICI Bank'];

    return (
        <div className="max-w-4xl mx-auto p-6">
            <h2 className="text-2xl font-bold mb-6 text-gray-900 dark:text-white">
                Indian Stock Market API Example
            </h2>

            <div className="mb-6">
                <h3 className="text-lg font-semibold mb-3 text-gray-800 dark:text-gray-200">
                    Try searching for these popular stocks:
                </h3>
                <div className="flex flex-wrap gap-2">
                    {exampleStocks.map((stock) => (
                        <button
                            key={stock}
                            onClick={() => searchStock(stock)}
                            disabled={loading}
                            className="px-4 py-2 bg-blue-500 hover:bg-blue-600 disabled:bg-blue-300 text-white rounded-lg transition-colors"
                        >
                            {stock}
                        </button>
                    ))}
                </div>
            </div>

            {loading && (
                <div className="flex items-center justify-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                    <span className="ml-2 text-gray-600 dark:text-gray-400">Loading...</span>
                </div>
            )}

            {error && (
                <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 mb-6">
                    <p className="text-red-600 dark:text-red-400">Error: {error}</p>
                </div>
            )}

            {stockData && (
                <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
                    <div className="border-b border-gray-200 dark:border-gray-700 pb-4 mb-4">
                        <h3 className="text-xl font-bold text-gray-900 dark:text-white">
                            {stockData.tickerId}
                        </h3>
                        <p className="text-gray-600 dark:text-gray-400">{stockData.companyName}</p>
                        <p className="text-sm text-gray-500 dark:text-gray-500">{stockData.industry}</p>
                    </div>

                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {stockData.currentPrice && (
                            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                                <h4 className="font-semibold text-gray-900 dark:text-white mb-2">Current Price</h4>
                                {stockData.currentPrice.NSE && (
                                    <p className="text-lg font-medium text-green-600 dark:text-green-400">
                                        NSE: ₹{stockData.currentPrice.NSE.toLocaleString()}
                                    </p>
                                )}
                                {stockData.currentPrice.BSE && (
                                    <p className="text-lg font-medium text-blue-600 dark:text-blue-400">
                                        BSE: ₹{stockData.currentPrice.BSE.toLocaleString()}
                                    </p>
                                )}
                            </div>
                        )}

                        {stockData.percentChange !== undefined && (
                            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                                <h4 className="font-semibold text-gray-900 dark:text-white mb-2">Change</h4>
                                <p className={`text-lg font-medium ${
                                    stockData.percentChange >= 0 ? 'text-green-600' : 'text-red-600'
                                }`}>
                                    {stockData.percentChange >= 0 ? '+' : ''}{stockData.percentChange.toFixed(2)}%
                                </p>
                            </div>
                        )}

                        {(stockData.yearHigh || stockData.yearLow) && (
                            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 md:col-span-2">
                                <h4 className="font-semibold text-gray-900 dark:text-white mb-2">52 Week Range</h4>
                                <div className="flex justify-between">
                                    {stockData.yearLow && (
                                        <p className="text-red-600 dark:text-red-400">
                                            Low: ₹{stockData.yearLow.toLocaleString()}
                                        </p>
                                    )}
                                    {stockData.yearHigh && (
                                        <p className="text-green-600 dark:text-green-400">
                                            High: ₹{stockData.yearHigh.toLocaleString()}
                                        </p>
                                    )}
                                </div>
                            </div>
                        )}
                    </div>
                </div>
            )}

            <div className="mt-8 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
                <h4 className="font-semibold text-yellow-800 dark:text-yellow-200 mb-2">Setup Required</h4>
                <p className="text-yellow-700 dark:text-yellow-300 text-sm">
                    To use this component, make sure you have set up your API key in the .env.local file:
                    <br />
                    <code className="bg-yellow-100 dark:bg-yellow-800 px-2 py-1 rounded mt-1 inline-block">
                        NEXT_PUBLIC_INDIAN_API_KEY=your_api_key_here
                    </code>
                </p>
            </div>
        </div>
    );
};

export default StockSearchExample;
