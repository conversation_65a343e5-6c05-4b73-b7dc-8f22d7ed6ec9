"use client";
import React, { useState } from 'react';

const ApiDebug = () => {
    const [debugInfo, setDebugInfo] = useState<any>(null);
    const [loading, setLoading] = useState(false);

    const checkApiKey = () => {
        const apiKey = process.env.NEXT_PUBLIC_INDIAN_API_KEY;
        setDebugInfo({
            type: 'API Key Check',
            hasApiKey: !!apiKey,
            apiKeyLength: apiKey?.length || 0,
            apiKeyPreview: apiKey ? `${apiKey.substring(0, 8)}...` : 'Not found',
            envVars: Object.keys(process.env).filter(key => key.startsWith('NEXT_PUBLIC_'))
        });
    };

    const testDirectFetch = async () => {
        setLoading(true);
        try {
            const apiKey = process.env.NEXT_PUBLIC_INDIAN_API_KEY;
            
            if (!apiKey) {
                setDebugInfo({
                    type: 'Direct Fetch Test',
                    error: 'API key not found in environment variables',
                    solution: 'Make sure NEXT_PUBLIC_INDIAN_API_KEY is set in .env.local'
                });
                return;
            }

            // Test with a simple stock search
            const url = 'https://indianapi.in/api/stock?name=TCS';
            
            console.log('Making request to:', url);
            console.log('With headers:', {
                'Accept': 'application/json',
                'x-api-key': `${apiKey.substring(0, 8)}...`
            });

            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'Accept': 'application/json',
                    'x-api-key': apiKey,
                    'Content-Type': 'application/json'
                }
            });

            console.log('Response status:', response.status);
            console.log('Response headers:', Object.fromEntries(response.headers.entries()));

            const responseText = await response.text();
            console.log('Response text:', responseText);

            let responseData;
            try {
                responseData = JSON.parse(responseText);
            } catch (e) {
                responseData = responseText;
            }

            setDebugInfo({
                type: 'Direct Fetch Test',
                url,
                status: response.status,
                statusText: response.statusText,
                ok: response.ok,
                headers: Object.fromEntries(response.headers.entries()),
                responseData,
                responseText: responseText.substring(0, 500) + (responseText.length > 500 ? '...' : '')
            });

        } catch (error) {
            console.error('Fetch error:', error);
            setDebugInfo({
                type: 'Direct Fetch Test',
                error: error instanceof Error ? error.message : 'Unknown error',
                errorDetails: error
            });
        } finally {
            setLoading(false);
        }
    };

    const testCors = async () => {
        setLoading(true);
        try {
            // Test CORS with a simple request
            const response = await fetch('https://indianapi.in/api/trending', {
                method: 'GET',
                headers: {
                    'Accept': 'application/json'
                }
            });

            setDebugInfo({
                type: 'CORS Test',
                status: response.status,
                corsHeaders: {
                    'Access-Control-Allow-Origin': response.headers.get('Access-Control-Allow-Origin'),
                    'Access-Control-Allow-Methods': response.headers.get('Access-Control-Allow-Methods'),
                    'Access-Control-Allow-Headers': response.headers.get('Access-Control-Allow-Headers')
                }
            });
        } catch (error) {
            setDebugInfo({
                type: 'CORS Test',
                error: error instanceof Error ? error.message : 'Unknown error',
                note: 'This might be a CORS issue'
            });
        } finally {
            setLoading(false);
        }
    };

    const testAlternativeEndpoint = async () => {
        setLoading(true);
        try {
            const apiKey = process.env.NEXT_PUBLIC_INDIAN_API_KEY;
            
            // Try the trending endpoint which might have different requirements
            const url = 'https://indianapi.in/api/trending';
            
            const response = await fetch(url, {
                method: 'GET',
                headers: {
                    'Accept': 'application/json',
                    'x-api-key': apiKey || '',
                }
            });

            const responseText = await response.text();
            let responseData;
            try {
                responseData = JSON.parse(responseText);
            } catch (e) {
                responseData = responseText;
            }

            setDebugInfo({
                type: 'Alternative Endpoint Test',
                url,
                status: response.status,
                statusText: response.statusText,
                ok: response.ok,
                responseData,
                responseText: responseText.substring(0, 500) + (responseText.length > 500 ? '...' : '')
            });

        } catch (error) {
            setDebugInfo({
                type: 'Alternative Endpoint Test',
                error: error instanceof Error ? error.message : 'Unknown error'
            });
        } finally {
            setLoading(false);
        }
    };

    return (
        <div className="max-w-4xl mx-auto p-6">
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
                API Debug Tool
            </h1>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
                <button
                    onClick={checkApiKey}
                    className="p-4 bg-blue-500 hover:bg-blue-600 text-white rounded-lg"
                >
                    Check API Key
                </button>
                
                <button
                    onClick={testDirectFetch}
                    disabled={loading}
                    className="p-4 bg-green-500 hover:bg-green-600 disabled:bg-gray-400 text-white rounded-lg"
                >
                    Test Direct Fetch
                </button>
                
                <button
                    onClick={testCors}
                    disabled={loading}
                    className="p-4 bg-yellow-500 hover:bg-yellow-600 disabled:bg-gray-400 text-white rounded-lg"
                >
                    Test CORS
                </button>
                
                <button
                    onClick={testAlternativeEndpoint}
                    disabled={loading}
                    className="p-4 bg-purple-500 hover:bg-purple-600 disabled:bg-gray-400 text-white rounded-lg"
                >
                    Test Alternative Endpoint
                </button>
            </div>

            {loading && (
                <div className="flex items-center justify-center py-8">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                    <span className="ml-3">Testing...</span>
                </div>
            )}

            {debugInfo && (
                <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
                    <h3 className="text-lg font-semibold mb-4">{debugInfo.type} Results</h3>
                    
                    <pre className="bg-gray-100 dark:bg-gray-700 p-4 rounded-lg overflow-auto text-sm">
                        {JSON.stringify(debugInfo, null, 2)}
                    </pre>

                    {debugInfo.error && (
                        <div className="mt-4 p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
                            <h4 className="font-semibold text-red-800 dark:text-red-200">Error Details:</h4>
                            <p className="text-red-600 dark:text-red-400">{debugInfo.error}</p>
                            
                            {debugInfo.solution && (
                                <div className="mt-2">
                                    <h5 className="font-medium text-red-800 dark:text-red-200">Solution:</h5>
                                    <p className="text-red-600 dark:text-red-400">{debugInfo.solution}</p>
                                </div>
                            )}
                        </div>
                    )}
                </div>
            )}

            <div className="mt-8 p-4 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                <h4 className="font-semibold text-blue-800 dark:text-blue-200 mb-2">Debug Checklist:</h4>
                <ul className="text-blue-700 dark:text-blue-300 text-sm space-y-1">
                    <li>✓ Check if API key is properly set in .env.local</li>
                    <li>✓ Verify API key is valid on indianapi.in dashboard</li>
                    <li>✓ Check if API has usage limits or is expired</li>
                    <li>✓ Test CORS configuration</li>
                    <li>✓ Try different endpoints to isolate the issue</li>
                    <li>✓ Check browser console for detailed error messages</li>
                </ul>
            </div>
        </div>
    );
};

export default ApiDebug;
