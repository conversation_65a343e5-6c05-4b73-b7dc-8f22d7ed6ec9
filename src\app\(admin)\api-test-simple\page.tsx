'use client';

import { useState } from 'react';
import { searchStock } from '@/utils/indianStockApi';

export default function SimpleApiTest() {
  const [result, setResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const testAPI = async () => {
    setLoading(true);
    setError(null);
    setResult(null);

    try {
      console.log('Testing Industry Search API with Banking...');
      const { searchIndustry } = await import('@/utils/indianStockApi');
      const response = await searchIndustry('Banking');

      if (response.error) {
        setError(response.error);
      } else {
        setResult(response.data);
        console.log('API Response:', response.data);
      }
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMessage);
      console.error('API Test Error:', err);
    } finally {
      setLoading(false);
    }
  };

  const testDirectFetch = async () => {
    setLoading(true);
    setError(null);
    setResult(null);

    try {
      console.log('Testing direct fetch...');
      const apiKey = process.env.NEXT_PUBLIC_INDIAN_API_KEY;
      console.log('API Key available:', !!apiKey);
      
      const response = await fetch('https://stock.indianapi.in/stock?name=TCS', {
        headers: {
          'x-api-key': apiKey || '',
          'Accept': 'application/json',
        }
      });

      console.log('Response status:', response.status);
      console.log('Response headers:', Object.fromEntries(response.headers.entries()));

      const text = await response.text();
      console.log('Response text:', text);

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${text}`);
      }

      const data = JSON.parse(text);
      setResult(data);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMessage);
      console.error('Direct Fetch Error:', err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Simple API Test</h1>
      
      <div className="space-y-4 mb-6">
        <button
          onClick={testAPI}
          disabled={loading}
          className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50 mr-4"
        >
          {loading ? 'Testing...' : 'Test Industry Search'}
        </button>

        <button
          onClick={testDirectFetch}
          disabled={loading}
          className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50"
        >
          {loading ? 'Testing...' : 'Test Direct Fetch'}
        </button>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          <strong>Error:</strong> {error}
        </div>
      )}

      {result && (
        <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded mb-4">
          <strong>Success!</strong> API is working. Check console for full response.
          <details className="mt-2">
            <summary className="cursor-pointer font-semibold">View Response Data</summary>
            <pre className="mt-2 text-xs overflow-auto max-h-96 bg-white p-2 rounded border">
              {JSON.stringify(result, null, 2)}
            </pre>
          </details>
        </div>
      )}

      <div className="bg-gray-100 p-4 rounded">
        <h3 className="font-semibold mb-2">Debug Info:</h3>
        <p><strong>API Key Present:</strong> {process.env.NEXT_PUBLIC_INDIAN_API_KEY ? 'Yes' : 'No'}</p>
        <p><strong>Base URL:</strong> https://stock.indianapi.in</p>
        <p><strong>Test Endpoint:</strong> /industry_search?query=Banking</p>
        <p><strong>Stock Details Endpoint:</strong> /stock?name=TCS</p>
      </div>
    </div>
  );
}
