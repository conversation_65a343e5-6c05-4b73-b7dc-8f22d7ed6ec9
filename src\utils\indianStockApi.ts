// Indian Stock Market API utility functions
// Documentation: https://indianapi.in/documentation/indian-stock-market

const API_BASE_URL = 'https://indianapi.in/api';

interface ApiResponse<T> {
    data?: T;
    error?: string;
}

// Get API key from environment variables
const getApiKey = (): string => {
    const apiKey = process.env.NEXT_PUBLIC_INDIAN_API_KEY;
    if (!apiKey) {
        throw new Error('NEXT_PUBLIC_INDIAN_API_KEY is not set in environment variables');
    }
    return apiKey;
};

// Generic API request function
const makeApiRequest = async <T>(endpoint: string, params?: Record<string, string>): Promise<ApiResponse<T>> => {
    try {
        const apiKey = getApiKey();
        const url = new URL(`${API_BASE_URL}${endpoint}`);

        // Add query parameters
        if (params) {
            Object.entries(params).forEach(([key, value]) => {
                url.searchParams.append(key, value);
            });
        }

        console.log('Making API request to:', url.toString());
        console.log('With API key:', apiKey ? `${apiKey.substring(0, 8)}...` : 'No API key');

        const response = await fetch(url.toString(), {
            method: 'GET',
            headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'x-api-key': apiKey,
            }
        });

        console.log('Response status:', response.status);
        console.log('Response headers:', Object.fromEntries(response.headers.entries()));

        // Get response text first to handle both JSON and non-JSON responses
        const responseText = await response.text();
        console.log('Response text:', responseText.substring(0, 200) + '...');

        if (!response.ok) {
            // Try to parse error response
            let errorMessage = `API request failed: ${response.status} ${response.statusText}`;
            try {
                const errorData = JSON.parse(responseText);
                if (errorData.message) {
                    errorMessage += ` - ${errorData.message}`;
                }
            } catch (e) {
                // If response is not JSON, include the text
                if (responseText) {
                    errorMessage += ` - ${responseText.substring(0, 100)}`;
                }
            }
            throw new Error(errorMessage);
        }

        // Parse JSON response
        let data;
        try {
            data = JSON.parse(responseText);
        } catch (e) {
            throw new Error(`Invalid JSON response: ${responseText.substring(0, 100)}`);
        }

        return { data };
    } catch (error) {
        console.error('API request error:', error);
        return { error: error instanceof Error ? error.message : 'Unknown error occurred' };
    }
};

// Stock search by company name
export const searchStock = async (name: string): Promise<ApiResponse<StockData>> => {
    return makeApiRequest<StockData>('/stock', { name });
};

// Industry search
export const searchIndustry = async (query: string): Promise<ApiResponse<IndustrySearchResult[]>> => {
    return makeApiRequest<IndustrySearchResult[]>('/industry_search', { query });
};

// Get trending stocks
export const getTrendingStocks = async () => {
    return makeApiRequest('/trending');
};

// Get 52 week high/low data
export const get52WeekHighLow = async () => {
    return makeApiRequest('/fetch_52_week_high_low_data');
};

// Get NSE most active stocks
export const getNSEMostActive = async () => {
    return makeApiRequest('/NSE_most_active');
};

// Get BSE most active stocks
export const getBSEMostActive = async () => {
    return makeApiRequest('/BSE_most_active');
};

// Get mutual funds data
export const getMutualFunds = async () => {
    return makeApiRequest('/mutual_funds');
};

// Search mutual funds
export const searchMutualFunds = async (query: string) => {
    return makeApiRequest('/mutual_fund_search', { query });
};

// Get price shockers
export const getPriceShockers = async () => {
    return makeApiRequest('/price_shockers');
};

// Get commodities data
export const getCommodities = async () => {
    return makeApiRequest('/commodities');
};

// Get stock target price and analyst recommendations
export const getStockTargetPrice = async (stockId: string) => {
    return makeApiRequest('/stock_target_price', { stock_id: stockId });
};

// Get stock forecasts
export const getStockForecasts = async (
    stockId: string,
    measureCode: string,
    periodType: 'Annual' | 'Interim',
    dataType: 'Actuals' | 'Estimates',
    age: 'OneWeekAgo' | 'ThirtyDaysAgo' | 'SixtyDaysAgo' | 'NinetyDaysAgo' | 'Current'
) => {
    return makeApiRequest('/stock_forecasts', {
        stock_id: stockId,
        measure_code: measureCode,
        period_type: periodType,
        data_type: dataType,
        age: age
    });
};

// Get historical data
export const getHistoricalData = async (
    stockName: string,
    period: '1m' | '6m' | '1yr' | '3yr' | '5yr' | '10yr' | 'max' = '5yr',
    filter: 'default' | 'price' | 'pe' | 'sm' | 'evebitda' | 'ptb' | 'mcs' = 'default'
) => {
    return makeApiRequest('/historical_data', {
        stock_name: stockName,
        period,
        filter
    });
};

// Get historical stats
export const getHistoricalStats = async (
    stockName: string,
    stats: 'quarter_results' | 'yoy_results' | 'balancesheet' | 'cashflow' | 'ratios' | 'shareholding_pattern_quarterly' | 'shareholding_pattern_yearly'
) => {
    return makeApiRequest('/historical_stats', {
        stock_name: stockName,
        stats
    });
};

// Type definitions for common API responses
export interface StockData {
    tickerId: string;
    companyName: string;
    industry: string;
    companyProfile?: any;
    currentPrice?: {
        BSE?: number;
        NSE?: number;
    };
    stockTechnicalData?: any;
    percentChange?: number;
    yearHigh?: number;
    yearLow?: number;
    financials?: any;
    keyMetrics?: any;
    futureExpiryDates?: string[];
    futureOverviewData?: any;
    initialStockFinancialData?: any;
    analystView?: any;
    recosBar?: any;
    riskMeter?: any;
    shareholding?: any;
    stockCorporateActionData?: any;
    stockDetailsReusableData?: any;
    recentNews?: any[];
}

export interface IndustrySearchResult {
    id: string;
    commonName: string;
    mgIndustry: string;
    mgSector: string;
    stockType: string;
    exchangeCodeBse: string;
    exchangeCodeNsi: string;
    bseRic: string;
    nseRic: string;
    activeStockTrends: {
        shortTermTrends: string;
        longTermTrends: string;
        overallRating: string;
    };
}

export interface TrendingStock {
    ticker_id: string;
    company_name: string;
    price: string;
    percent_change: string;
    net_change: string;
    bid: string;
    ask: string;
    high: string;
    low: string;
    open: string;
    low_circuit_limit: string;
    up_circuit_limit: string;
    volume: string;
    date: string;
    time: string;
    close: string;
    bid_size: string;
    ask_size: string;
    average_price: string;
    exchange_type: string;
    lot_size: string;
    average_volume: string;
    deviation: string;
    actual_deviation: string;
    no_of_days_for_average: string;
    overall_rating: string;
    short_term_trends: string;
    long_term_trends: string;
    year_low: string;
    year_high: string;
    ric: string;
}

export interface TrendingStocksResponse {
    trending_stocks: {
        top_gainers: TrendingStock[];
        top_losers: TrendingStock[];
    };
}
