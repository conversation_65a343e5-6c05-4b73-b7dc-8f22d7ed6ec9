'use client';

import { useState } from 'react';

export default function ApiDebugPage() {
  const [response, setResponse] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const testApiCall = async (stockName: string) => {
    setLoading(true);
    setError(null);
    setResponse(null);
    
    try {
      const apiKey = process.env.NEXT_PUBLIC_INDIAN_API_KEY;
      console.log('API Key:', apiKey ? 'Present' : 'Missing');
      
      const url = `https://stock.indianapi.in/stock?name=${encodeURIComponent(stockName)}`;
      console.log('Request URL:', url);
      
      const response = await fetch(url, {
        headers: {
          'x-api-key': apiKey || '',
          'Accept': 'application/json',
        }
      });

      console.log('Response Status:', response.status);
      console.log('Response Headers:', Object.fromEntries(response.headers.entries()));

      if (!response.ok) {
        const errorText = await response.text();
        console.log('Error Response Body:', errorText);
        throw new Error(`HTTP ${response.status}: ${response.statusText}\n${errorText}`);
      }

      const data = await response.json();
      console.log('Success Response:', data);
      setResponse(data);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMessage);
      console.error('API Error:', err);
    } finally {
      setLoading(false);
    }
  };

  const testStocks = ['TCS', 'Reliance', 'HDFC Bank', 'Infosys', 'ICICI Bank', 'Tata Steel'];

  return (
    <div className="p-6 max-w-6xl mx-auto">
      <h1 className="text-3xl font-bold mb-6 text-gray-900 dark:text-white">API Debug Tool</h1>
      
      <div className="mb-6">
        <h2 className="text-lg font-semibold mb-3 text-gray-800 dark:text-gray-200">Test Stock API:</h2>
        <div className="flex flex-wrap gap-2 mb-4">
          {testStocks.map((stock) => (
            <button
              key={stock}
              onClick={() => testApiCall(stock)}
              disabled={loading}
              className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
            >
              {stock}
            </button>
          ))}
        </div>
        
        <div className="flex gap-2">
          <input
            type="text"
            placeholder="Enter custom stock name"
            className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                const target = e.target as HTMLInputElement;
                if (target.value.trim()) {
                  testApiCall(target.value.trim());
                }
              }
            }}
          />
          <button
            onClick={() => {
              const input = document.querySelector('input[type="text"]') as HTMLInputElement;
              if (input?.value.trim()) {
                testApiCall(input.value.trim());
              }
            }}
            disabled={loading}
            className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50"
          >
            {loading ? 'Testing...' : 'Test'}
          </button>
        </div>
      </div>

      {/* Loading State */}
      {loading && (
        <div className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
          <span className="ml-2 text-gray-600 dark:text-gray-400">Testing API...</span>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          <h3 className="font-bold">Error:</h3>
          <pre className="text-sm mt-2 whitespace-pre-wrap">{error}</pre>
        </div>
      )}

      {/* Success Response */}
      {response && (
        <div className="space-y-4">
          <div className="bg-green-100 border border-green-400 text-green-700 px-4 py-3 rounded">
            <h3 className="font-bold">✅ API Call Successful!</h3>
          </div>
          
          {/* Data Type Analysis */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">Data Type Analysis</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              {Object.entries(response).map(([key, value]) => (
                <div key={key} className="border-b border-gray-200 dark:border-gray-700 pb-2">
                  <span className="font-medium text-gray-700 dark:text-gray-300">
                    {key}:
                  </span>
                  <div className="text-gray-600 dark:text-gray-400">
                    <div>Type: <code className="bg-gray-100 dark:bg-gray-700 px-1 rounded">{typeof value}</code></div>
                    <div>Value: <code className="bg-gray-100 dark:bg-gray-700 px-1 rounded break-all">
                      {typeof value === 'object' && value !== null 
                        ? JSON.stringify(value)
                        : String(value)
                      }
                    </code></div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Raw JSON Response */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
            <h3 className="text-lg font-semibold mb-4 text-gray-900 dark:text-white">Raw JSON Response</h3>
            <pre className="text-xs text-gray-600 dark:text-gray-400 overflow-auto max-h-96 bg-gray-50 dark:bg-gray-900 p-4 rounded border">
              {JSON.stringify(response, null, 2)}
            </pre>
          </div>
        </div>
      )}
    </div>
  );
}
