"use client";
import React, { useState, useEffect } from 'react';
import { searchIndustry, type IndustrySearchResult } from '@/utils/indianStockApi';
import { XMarkIcon } from '@heroicons/react/24/outline';

interface StockDetails {
    tickerId?: string;
    companyName?: string;
    industry?: string;
    currentPrice?: {
        BSE?: number;
        NSE?: number;
    };
    percentChange?: number;
    yearHigh?: number;
    yearLow?: number;
    [key: string]: any; // Allow any additional properties
}

const SearchBar = () => {
    const [searchQuery, setSearchQuery] = useState('');
    const [industryResults, setIndustryResults] = useState<IndustrySearchResult[]>([]);
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const [selectedStock, setSelectedStock] = useState<StockDetails | null>(null);
    const [isLoadingDetails, setIsLoadingDetails] = useState(false);
    const [showDetails, setShowDetails] = useState(false);
    const [mounted, setMounted] = useState(false);

    // Fix hydration issues
    useEffect(() => {
        setMounted(true);
    }, []);

    // Debounce search to avoid too many API calls
    useEffect(() => {
        const handleDebouncedSearch = async () => {
            if (!searchQuery.trim()) return;

            setIsLoading(true);
            setError(null);

            try {
                const response = await searchIndustry(searchQuery);

                if (response.error) {
                    throw new Error(response.error);
                }

                if (response.data && Array.isArray(response.data)) {
                    setIndustryResults(response.data.slice(0, 10)); // Limit to 10 results
                } else {
                    setIndustryResults([]);
                    setError('No companies found in that industry');
                }
            } catch (err) {
                setError(err instanceof Error ? err.message : 'An error occurred while searching');
                setIndustryResults([]);
            } finally {
                setIsLoading(false);
            }
        };

        const timeoutId = setTimeout(() => {
            if (searchQuery.trim() && searchQuery.length > 2) {
                handleDebouncedSearch();
            } else {
                setIndustryResults([]);
            }
        }, 500);

        return () => clearTimeout(timeoutId);
    }, [searchQuery]);

    const fetchStockDetails = async (stockName: string) => {
        setIsLoadingDetails(true);
        try {
            const apiKey = process.env.NEXT_PUBLIC_INDIAN_API_KEY;
            const response = await fetch(`https://stock.indianapi.in/stock?name=${encodeURIComponent(stockName)}`, {
                headers: {
                    'x-api-key': apiKey || '',
                    'Accept': 'application/json',
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();
            setSelectedStock(data);
            setShowDetails(true);
        } catch (error) {
            console.error('Error fetching stock details:', error);
            alert('Failed to fetch stock details. Please try again.');
        } finally {
            setIsLoadingDetails(false);
        }
    };

    const handleResultClick = (result: IndustrySearchResult) => {
        fetchStockDetails(result.commonName);
    };

    const closeDetails = () => {
        setShowDetails(false);
        setSelectedStock(null);
    };



    return (
        <>
            <div className="w-full max-w-3xl mx-auto px-4">
                <form onSubmit={(e) => e.preventDefault()} className="relative">
                    <input
                        type="text"
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                        placeholder="Search by industry (e.g., Banking, IT, Pharma)..."
                        className="w-full h-14 pl-6 pr-12 rounded-xl text-grey-500 border border-color-gray-600 bg-transparent outline-none focus:border-primary dark:bg-gray-900 dark:border-strokedark dark:focus:border-primary dark:text-white border border-stroke bg-transparent outline-none focus:border-primary dark:border-strokedark dark:bg-meta-4 dark:focus:border-primary"
                    />
                    <button
                        type="submit"
                        className="absolute right-4 top-1/2 -translate-y-1/2"
                        disabled={isLoading}
                    >
                        {isLoading ? (
                            <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-primary"></div>
                        ) : (
                            <svg
                                className="fill-body hover:fill-primary dark:fill-bodydark dark:hover:fill-primary"
                                width="20"
                                height="20"
                                viewBox="0 0 20 20"
                                fill="none"
                                xmlns="http://www.w3.org/2000/svg"
                            >
                                <path
                                    fillRule="evenodd"
                                    clipRule="evenodd"
                                    d="M9.16666 3.33332C5.945 3.33332 3.33332 5.945 3.33332 9.16666C3.33332 12.3883 5.945 15 9.16666 15C12.3883 15 15 12.3883 15 9.16666C15 5.945 12.3883 3.33332 9.16666 3.33332ZM1.66666 9.16666C1.66666 5.02452 5.02452 1.66666 9.16666 1.66666C13.3088 1.66666 16.6667 5.02452 16.6667 9.16666C16.6667 13.3088 13.3088 16.6667 9.16666 16.6667C5.02452 16.6667 1.66666 13.3088 1.66666 9.16666Z"
                                />
                                <path
                                    fillRule="evenodd"
                                    clipRule="evenodd"
                                    d="M13.2857 13.2857C13.6112 12.9603 14.1388 12.9603 14.4642 13.2857L18.0892 16.9107C18.4147 17.2362 18.4147 17.7638 18.0892 18.0892C17.7638 18.4147 17.2362 18.4147 16.9107 18.0892L13.2857 14.4642C12.9603 14.1388 12.9603 13.6112 13.2857 13.2857Z"
                                />
                            </svg>
                        )}
                    </button>
                </form>

                {error && (
                    <div className="mt-4 p-4 bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 rounded-lg">
                        {error}
                    </div>
                )}

                {/* Industry Results */}
                {industryResults.length > 0 && (
                    <div className="mt-4 bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden">
                        <div className="max-h-96 overflow-y-auto">
                            {industryResults.map((result) => (
                                <div
                                    key={result.id}
                                    onClick={() => handleResultClick(result)}
                                    className="p-4 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer border-b border-gray-100 dark:border-gray-700 last:border-0"
                                >
                                    <div className="flex justify-between items-start">
                                        <div className="flex-1">
                                            <h3 className="font-medium text-gray-900 dark:text-white">
                                                {result.commonName}
                                            </h3>
                                            <p className="text-sm text-gray-500 dark:text-gray-400">
                                                {result.mgIndustry} • {result.mgSector}
                                            </p>
                                            <div className="flex gap-2 mt-1">
                                                <span className="text-xs px-2 py-1 bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded">
                                                    BSE: {result.exchangeCodeBse}
                                                </span>
                                                <span className="text-xs px-2 py-1 bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 rounded">
                                                    NSE: {result.exchangeCodeNsi}
                                                </span>
                                            </div>
                                        </div>
                                        <div className="text-right">
                                            <div className={`text-xs px-2 py-1 rounded ${
                                                result.activeStockTrends.overallRating === 'Bullish'
                                                    ? 'bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200'
                                                    : result.activeStockTrends.overallRating === 'Bearish'
                                                    ? 'bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200'
                                                    : 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200'
                                            }`}>
                                                {result.activeStockTrends.overallRating}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>
                )}
            </div>

            {/* Stock Details Modal - Only render on client side */}
            {typeof window !== 'undefined' && showDetails && (
                <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
                    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
                        {/* Modal Header */}
                        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
                            <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                                Stock Details
                            </h2>
                            <button
                                onClick={closeDetails}
                                className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
                            >
                                <XMarkIcon className="h-6 w-6" />
                            </button>
                        </div>

                        {/* Modal Content */}
                        <div className="p-6">
                            {isLoadingDetails ? (
                                <div className="flex items-center justify-center py-8">
                                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
                                    <span className="ml-2 text-gray-600 dark:text-gray-400">Loading stock details...</span>
                                </div>
                            ) : selectedStock ? (
                                <div className="space-y-6">
                                    {/* Company Info */}
                                    <div>
                                        <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                                            {selectedStock.companyName}
                                        </h3>
                                        <p className="text-sm text-gray-600 dark:text-gray-400">
                                            {selectedStock.industry} • {selectedStock.tickerId}
                                        </p>
                                    </div>

                                    {/* Price Information */}
                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                        {selectedStock.currentPrice?.NSE && (
                                            <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                                                <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">NSE Price</h4>
                                                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                                                    ₹{selectedStock.currentPrice.NSE.toFixed(2)}
                                                </p>
                                            </div>
                                        )}
                                        {selectedStock.currentPrice?.BSE && (
                                            <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                                                <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">BSE Price</h4>
                                                <p className="text-2xl font-bold text-gray-900 dark:text-white">
                                                    ₹{selectedStock.currentPrice.BSE.toFixed(2)}
                                                </p>
                                            </div>
                                        )}
                                    </div>

                                    {/* Performance Metrics */}
                                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                        {selectedStock.percentChange !== undefined && (
                                            <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                                                <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">Change</h4>
                                                <p className={`text-lg font-semibold ${
                                                    selectedStock.percentChange >= 0
                                                        ? 'text-green-600 dark:text-green-400'
                                                        : 'text-red-600 dark:text-red-400'
                                                }`}>
                                                    {selectedStock.percentChange >= 0 ? '+' : ''}{selectedStock.percentChange.toFixed(2)}%
                                                </p>
                                            </div>
                                        )}
                                        {selectedStock.yearHigh && (
                                            <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                                                <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">52W High</h4>
                                                <p className="text-lg font-semibold text-gray-900 dark:text-white">
                                                    ₹{selectedStock.yearHigh.toFixed(2)}
                                                </p>
                                            </div>
                                        )}
                                        {selectedStock.yearLow && (
                                            <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                                                <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300">52W Low</h4>
                                                <p className="text-lg font-semibold text-gray-900 dark:text-white">
                                                    ₹{selectedStock.yearLow.toFixed(2)}
                                                </p>
                                            </div>
                                        )}
                                    </div>

                                    {/* Stock Details in a more readable format */}
                                    <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                                        <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-4">Stock Information</h4>
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                                            {Object.entries(selectedStock).map(([key, value]) => {
                                                if (key === 'currentPrice' && value && typeof value === 'object') {
                                                    return (
                                                        <div key={key} className="space-y-1">
                                                            <span className="font-medium text-gray-700 dark:text-gray-300">Current Prices:</span>
                                                            {value.NSE && <div>NSE: ₹{value.NSE}</div>}
                                                            {value.BSE && <div>BSE: ₹{value.BSE}</div>}
                                                        </div>
                                                    );
                                                }
                                                if (typeof value === 'object' && value !== null) {
                                                    return null; // Skip complex objects for now
                                                }
                                                return (
                                                    <div key={key} className="flex justify-between">
                                                        <span className="font-medium text-gray-700 dark:text-gray-300 capitalize">
                                                            {key.replace(/([A-Z])/g, ' $1').trim()}:
                                                        </span>
                                                        <span className="text-gray-600 dark:text-gray-400">
                                                            {String(value)}
                                                        </span>
                                                    </div>
                                                );
                                            })}
                                        </div>
                                    </div>

                                    {/* Raw JSON Data - Collapsible */}
                                    <details className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
                                        <summary className="text-sm font-medium text-gray-700 dark:text-gray-300 cursor-pointer mb-2">
                                            View Raw JSON Data
                                        </summary>
                                        <pre className="text-xs text-gray-600 dark:text-gray-400 overflow-auto max-h-60 bg-white dark:bg-gray-800 p-3 rounded border">
                                            {JSON.stringify(selectedStock, null, 2)}
                                        </pre>
                                    </details>
                                </div>
                            ) : (
                                <div className="text-center py-8">
                                    <p className="text-gray-600 dark:text-gray-400">No stock details available</p>
                                </div>
                            )}
                        </div>
                    </div>
                </div>
            )}
        </>
    );
};

export default SearchBar;
